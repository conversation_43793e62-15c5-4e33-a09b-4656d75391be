#include "led.h"

//����ledֻҪ��cubemax��xѡ�����ŵ������GPIO output
//������ֲֻҪ��������������ž�����
uint8_t ucLed[6] = {0,0,0,0,0,0};
void led_disp(uint8_t *ucLed)
{
    uint8_t temp = 0x00;
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {

     if (ucLed[i]) temp |= (1 << i);
    }


    if (temp != temp_old)
    {

        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 0 (PD8)
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 1 (PD9)
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, (temp & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 2 (PB10)
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3 (PB11)
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12,  (temp & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4 (PD12)
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13,  (temp & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 5 (PD13)

        temp_old = temp;
    }
}


void led_task(void)
{
    static uint32_t led_counter = 0;

    // 添加LED测试效果：流水灯
    led_counter++;
    if(led_counter >= 100) { // 每1秒切换一次（10ms * 100）
        led_counter = 0;
        static uint8_t led_index = 0;

        // 清除所有LED
        for(int i = 0; i < 6; i++) {
            ucLed[i] = 0;
        }

        // 点亮当前LED
        ucLed[led_index] = 1;

        // 移动到下一个LED
        led_index = (led_index + 1) % 6;
    }

    led_disp(ucLed);
}

