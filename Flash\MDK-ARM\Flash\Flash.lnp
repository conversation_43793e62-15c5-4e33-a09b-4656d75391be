--cpu=Cortex-M4.fp.sp
"flash\startup_stm32f427xx.o"
"flash\main.o"
"flash\gpio.o"
"flash\adc.o"
"flash\dma.o"
"flash\i2c.o"
"flash\spi.o"
"flash\usart.o"
"flash\stm32f4xx_it.o"
"flash\stm32f4xx_hal_msp.o"
"flash\stm32f4xx_hal_adc.o"
"flash\stm32f4xx_hal_adc_ex.o"
"flash\stm32f4xx_ll_adc.o"
"flash\stm32f4xx_hal_rcc.o"
"flash\stm32f4xx_hal_rcc_ex.o"
"flash\stm32f4xx_hal_flash.o"
"flash\stm32f4xx_hal_flash_ex.o"
"flash\stm32f4xx_hal_flash_ramfunc.o"
"flash\stm32f4xx_hal_gpio.o"
"flash\stm32f4xx_hal_dma_ex.o"
"flash\stm32f4xx_hal_dma.o"
"flash\stm32f4xx_hal_pwr.o"
"flash\stm32f4xx_hal_pwr_ex.o"
"flash\stm32f4xx_hal_cortex.o"
"flash\stm32f4xx_hal.o"
"flash\stm32f4xx_hal_exti.o"
"flash\stm32f4xx_hal_i2c.o"
"flash\stm32f4xx_hal_i2c_ex.o"
"flash\stm32f4xx_hal_spi.o"
"flash\stm32f4xx_hal_uart.o"
"flash\system_stm32f4xx.o"
"flash\adc_app.o"
"flash\key.o"
"flash\led.o"
"flash\oled_app.o"
"flash\schedule.o"
"flash\uart.o"
"flash\ebtn.o"
"flash\ringbuffer.o"
"flash\mui.o"
"flash\mui_u8g2.o"
"flash\u8g2_arc.o"
"flash\u8g2_bitmap.o"
"flash\u8g2_box.o"
"flash\u8g2_buffer.o"
"flash\u8g2_button.o"
"flash\u8g2_circle.o"
"flash\u8g2_cleardisplay.o"
"flash\u8g2_d_memory.o"
"flash\u8g2_d_setup.o"
"flash\u8g2_font.o"
"flash\u8g2_fonts.o"
"flash\u8g2_hvline.o"
"flash\u8g2_input_value.o"
"flash\u8g2_intersection.o"
"flash\u8g2_kerning.o"
"flash\u8g2_line.o"
"flash\u8g2_ll_hvline.o"
"flash\u8g2_message.o"
"flash\u8g2_polygon.o"
"flash\u8g2_selection_list.o"
"flash\u8g2_setup.o"
"flash\u8log.o"
"flash\u8log_u8g2.o"
"flash\u8log_u8x8.o"
"flash\u8x8_8x8.o"
"flash\u8x8_byte.o"
"flash\u8x8_cad.o"
"flash\u8x8_capture.o"
"flash\u8x8_d_ssd1306_128x32.o"
"flash\u8x8_debounce.o"
"flash\u8x8_display.o"
"flash\u8x8_fonts.o"
"flash\u8x8_gpio.o"
"flash\u8x8_input_value.o"
"flash\u8x8_message.o"
"flash\u8x8_selection_list.o"
"flash\u8x8_setup.o"
"flash\u8x8_string.o"
"flash\u8x8_u8toa.o"
"flash\u8x8_u16toa.o"
--strict --scatter "Flash\Flash.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Flash.map" -o Flash\Flash.axf