<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Flash</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F470VE</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F4xx_DFP.3.0.3</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x030000) IRAM2(0x10000000,0x010000) IROM(0x08000000,0x080000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F4xx_512KB -********** -FL080000 -FP0($$Device:GD32F470VE$Flash\GD32F4xx_512KB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:GD32F470VE$Device\F4XX\Include\gd32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:GD32F470VE$SVD\GD32F4xx.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>Flash\</OutputDirectory>
          <OutputName>Flash</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath></ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>0</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>5</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_HAL_DRIVER,STM32F427xx</Define>
              <Undefine></Undefine>
              <IncludePath>../Core/Inc;../Drivers/STM32F4xx_HAL_Driver/Inc;../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy;../Drivers/CMSIS/Device/ST/STM32F4xx/Include;../Drivers/CMSIS/Include;..\APP;..\component\ebtn;..\component\ringbuffer;..\component\u8g2</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application/MDK-ARM</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f427xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>startup_stm32f427xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Application/User/Core</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/main.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/gpio.c</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/adc.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/dma.c</FilePath>
            </File>
            <File>
              <FileName>i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/i2c.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/spi.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/stm32f4xx_hal_msp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/STM32F4xx_HAL_Driver</GroupName>
          <Files>
            <File>
              <FileName>stm32f4xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_ll_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ramfunc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_i2c_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/system_stm32f4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>APP</GroupName>
          <Files>
            <File>
              <FileName>adc_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\adc_app.c</FilePath>
            </File>
            <File>
              <FileName>adc_app.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\adc_app.h</FilePath>
            </File>
            <File>
              <FileName>key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\key.c</FilePath>
            </File>
            <File>
              <FileName>key.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\key.h</FilePath>
            </File>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\led.c</FilePath>
            </File>
            <File>
              <FileName>led.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\led.h</FilePath>
            </File>
            <File>
              <FileName>mydefine.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\mydefine.h</FilePath>
            </File>
            <File>
              <FileName>oled_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\oled_app.c</FilePath>
            </File>
            <File>
              <FileName>oled_app.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\oled_app.h</FilePath>
            </File>
            <File>
              <FileName>schedule.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\schedule.c</FilePath>
            </File>
            <File>
              <FileName>schedule.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\schedule.h</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\uart.c</FilePath>
            </File>
            <File>
              <FileName>uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\uart.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>component/ebtn</GroupName>
          <Files>
            <File>
              <FileName>bit_array.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\component\ebtn\bit_array.h</FilePath>
            </File>
            <File>
              <FileName>ebtn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\ebtn\ebtn.c</FilePath>
            </File>
            <File>
              <FileName>ebtn.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\component\ebtn\ebtn.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>component/ringbuffer</GroupName>
          <Files>
            <File>
              <FileName>ringbuffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\ringbuffer\ringbuffer.c</FilePath>
            </File>
            <File>
              <FileName>ringbuffer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\component\ringbuffer\ringbuffer.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>component/u8g2</GroupName>
          <Files>
            <File>
              <FileName>mui.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\mui.c</FilePath>
            </File>
            <File>
              <FileName>mui_u8g2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\mui_u8g2.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_arc.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_bitmap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_bitmap.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_box.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_box.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_buffer.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_button.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_button.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_circle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_circle.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_cleardisplay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_cleardisplay.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_d_memory.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_d_memory.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_d_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_d_setup.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_font.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_fonts.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_fonts.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_hvline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_hvline.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_input_value.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_input_value.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_intersection.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_intersection.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_kerning.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_kerning.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_line.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_ll_hvline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_ll_hvline.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_message.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_message.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_polygon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_polygon.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_selection_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_selection_list.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8g2_setup.c</FilePath>
            </File>
            <File>
              <FileName>u8log.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8log.c</FilePath>
            </File>
            <File>
              <FileName>u8log_u8g2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8log_u8g2.c</FilePath>
            </File>
            <File>
              <FileName>u8log_u8x8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8log_u8x8.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_8x8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_8x8.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_byte.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_byte.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_cad.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_cad.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_capture.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_capture.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_d_ssd1306_128x32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_d_ssd1306_128x32.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_debounce.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_debounce.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_display.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_display.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_fonts.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_fonts.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_gpio.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_input_value.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_input_value.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_message.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_message.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_selection_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_selection_list.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_setup.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_string.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_string.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_u8toa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_u8toa.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_u16toa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\component\u8g2\u8x8_u16toa.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="6.1.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.7.36" url="https://www.keil.com/pack/" vendor="ARM" version="6.1.0"/>
        <targetInfos>
          <targetInfo name="Flash"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>Flash</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
