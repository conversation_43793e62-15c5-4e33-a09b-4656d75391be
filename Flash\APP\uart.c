#include "uart.h"
uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};
uint8_t uart_flag = 0;
uint8_t uart_ringbuffer_data[128];
struct rt_ringbuffer uart_ringbuffer;
//�������� �ȵ������RX��TX���� Ȼ���� ����ļ��Ŵ��ڶ���ģʽ����ʹ��ΪAsynchronous
//��NVIC�д򿪶�Ӧ�����ж� �����������RX����DMA ģʽΪcircular Data WidthΪ Word
//��ֲ����ringbuffer��� ������mydefine.h������ringbuffer.hͷ�ļ�  Ȼ��������uint8_t uart_ringbuffer_data[128];struct rt_ringbuffer uart_ringbuffer;���¶�������
//��main.c�г�ʼ�� Uart_Inti()�������  ps�������ܿ���ֱ����

int my_printf(UART_HandleTypeDef*huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	// ��ʼ���ɱ�����б�
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}//HAL_UART_Receive_IT();


void Uart_Inti()
{
	  rt_ringbuffer_init(&uart_ringbuffer, uart_ringbuffer_data, sizeof(uart_ringbuffer_data));
    HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
   __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
}

/**
 * @brief UART DMA������ɻ�����¼��ص�����
 * @param huart UART���
 * @param Size ָʾ���¼�����ǰ��DMA�Ѿ��ɹ������˶����ֽڵ�����
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 1. ȷ����Ŀ�괮�� (USART1)
    if (huart->Instance == USART1)
    {
        // 2. ����ֹͣ��ǰ�� DMA ���� (������ڽ�����)
        //    ��Ϊ�����ж���ζ�ŷ��ͷ��Ѿ�ֹͣ����ֹ DMA �����ȴ������
        HAL_UART_DMAStop(huart);

        // �����յ�������д�뻷�λ�����
        rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);
        // ע�⣺����ʹ���� Size��ֻ����ʵ�ʽ��յ�������

        // 4. ����"����֪ͨ��"��������ѭ�������ݴ�����
        uart_flag = 1;

        // 5. ��� DMA ���ջ�������Ϊ�´ν�����׼��
        //    ��Ȼ memcpy ֻ������ Size �������������������������
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        // 6. **�ؼ�������������һ�� DMA ���н���**
        //    �����ٴε��ã�����ֻ�������һ��
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

        // 7. ���֮ǰ�ر��˰����жϣ�������Ҫ�������ٴιر� (������Ҫ)
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}

void uart_task(void)
{
    static uint32_t uart_counter = 0;

    // 定期发送心跳信息
    uart_counter++;
    if(uart_counter >= 1000) { // 每10秒发送一次（10ms * 1000）
        uart_counter = 0;
        my_printf(&huart1, "System Running - Tick: %lu\r\n", HAL_GetTick());
    }

    // 如果标志位为0，说明没有数据需要处理，直接返回
    if (uart_flag == 0)
        return;

    uart_flag = 0;

    uint16_t length;
    length = rt_ringbuffer_data_len(&uart_ringbuffer);
    if (length > 0)
    {
        rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);
        my_printf(&huart1,"Received: %s\r\n",uart_dma_buffer);

        // 添加简单的命令处理
        if(strncmp((char*)uart_dma_buffer,"led",3)==0)
        {
            ucLed[1] = !ucLed[1]; // 切换LED1状态
            my_printf(&huart1,"LED1 toggled\r\n");
        }

        memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
    }
}

